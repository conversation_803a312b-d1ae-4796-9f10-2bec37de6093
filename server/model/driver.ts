import mongoose, { Schem<PERSON> } from "mongoose";
import { DriverInterface } from "../Interface/drive.interface";

const DriverSchema: Schema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true },
  password: { type: String, required: true },
  isAvailable: { type: Boolean, default: true },
  AssignedZones: { type: [mongoose.Types.ObjectId], ref: 'Driver', default: [] },
  AssignedOrders: { type: [mongoose.Types.ObjectId], ref: 'Order', default: [] },
  JoinedOn: { type: Date, default: Date.now },
});

export default mongoose.model<DriverInterface>("Driver", DriverSchema);
