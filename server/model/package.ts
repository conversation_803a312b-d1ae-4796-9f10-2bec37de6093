import mongoose, { Schem<PERSON> } from "mongoose";
import { PackageInterface } from "../Interface/package.interface";

const PackageSchema: Schema = new mongoose.Schema({
  packageName: { type: String, required: true },
  height: { type: Number, required: true },
  width: { type: Number, required: true },
  length: { type: Number, required: true },
  weight: { type: Number, required: true },
});

export default mongoose.model<PackageInterface>("Package", PackageSchema);
