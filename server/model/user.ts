import mongoose, { Schem<PERSON> } from "mongoose";
import { UserInterface } from "../Interface/user.interface";

const UserSchema:Schema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true },
  password: { type: String, required: true },
  joinedOn: { type: Date, default: Date.now() },
});

export default mongoose.model<UserInterface>("Users", UserSchema);
