import mongoose, { Schem<PERSON> } from "mongoose";
import { OrderInterface } from "../Interface/order.interface";
import location from "./location";
import _package from "./package";
import quote from "./quote";

const OrderSchema: Schema = new mongoose.Schema({
    PickupLocation: location,
    DeliveryLocation: location,
    Packages: [_package],
    Quote: quote,
    OrderStatus: { type: String, required: true },
    IsPaid: { type: Boolean, default: false },
    AssignedDrivers: { type: Schema.Types.ObjectId, ref: 'Driver' },
    CreatedAt: { type: Date, default: Date.now }
});

export default mongoose.model<OrderInterface>("Order", OrderSchema);
