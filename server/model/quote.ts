import mongoose, { Schem<PERSON> } from "mongoose";
import _package from "./package";
import { QuoteInterface } from "../Interface/quote.interface";

const QuoteSchema: Schema = new mongoose.Schema({
    ServiceName: { type: String, required: true },
    PriceAddition: { type: String, required: true },
    HourAddition: { type: String, required: true },
    Users: [{ type: Schema.Types.ObjectId, ref: 'User', default: [] }]
});

export default mongoose.model<QuoteInterface>("Quote", QuoteSchema);




