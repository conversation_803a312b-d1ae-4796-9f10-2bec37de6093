import mongoose, { Schema } from "mongoose";
import { LocationInterface } from "../Interface/location.interface";

const LocationSchema: Schema = new mongoose.Schema({
  addressLine1: { type: String, required: true },
  city: { type: String, required: true },
  state: { type: String, required: true },
  postalCode: { type: String, required: true },
  isFavoriteForPickupLocation: { type: Boolean, default: false },
  isFavoriteForDeliveryLocation: { type: Boolean, default: false },
});

export default mongoose.model<LocationInterface>("Location", LocationSchema);
