{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "tsc && nodemon --unhandled-rejections=strict ./dist/server.js", "build": "tsc --build"}, "author": "<PERSON><PERSON><PERSON>", "type": "commonjs", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.2.1", "nodemon": "^3.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "ts-node": "^10.9.2", "typescript": "^5.4.2"}}