import { Request, Response } from "express";
import location from "../model/location";
import { LocationInterface } from "../Interface/location.interface";
import { HttpException } from "../constants/httpException";

class LocationController {
  public async addLocation(req: Request, res: Response): Promise<void> {
    const locationPayload = req.body;
    try {
      const locationCreated: LocationInterface = await location.create({
        locationPayload,
      });
      res
        .status(HttpException.SUCCESS)
        .json({ locationCreated, message: "Location created Successfully" });
    } catch (error) {
      console.log("ERROR while adding Location", error);
      res
        .status(HttpException.NOT_FOUND)
        .json({ message: "error while adding Location", error });
    }
  }

  public async updateLocation(req: Request, res: Response): Promise<any> {
    const locationPayload: LocationInterface = req.body;
    try {
      const updatedLocation: LocationInterface | null =
        await location.findByIdAndUpdate(
          { _id: locationPayload?._id },
          { locationPayload }
        );
      if (!updatedLocation) {
        console.log("location not exist in DB");
        return res
          .status(HttpException.FORBIDDEN)
          .json({ message: "location not exist Please refresh the page" });
      }
      res
        .status(HttpException.SUCCESS)
        .json({ message: "Location updated Successfully" });
    } catch (error) {
      console.log("ERROR while updating Location", error);
      res
        .status(HttpException.NOT_FOUND)
        .json({ message: "error while updating Location", error });
    }
  }
  public async deleteLocation(req: Request, res: Response): Promise<any> {
    const { locationId } = req.body;
    try {
      await location.findByIdAndDelete({ _id: locationId });
      res
        .status(HttpException.SUCCESS)
        .json({ message: "Location deleted Successfully" });
    } catch (error) {
      console.log("ERROR while deleting Location", error);
      res
        .status(HttpException.NOT_FOUND)
        .json({ message: "error while deleting Location", error });
    }
  }
}

export default new LocationController();
