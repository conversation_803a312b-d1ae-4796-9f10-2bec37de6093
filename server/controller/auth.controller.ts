import {
  UserInterface,
  UserSignupInterface,
} from "../Interface/user.interface";
import User from "../model/user";
import { Request, Response } from "express";
import { HttpException } from "../constants/httpException";
import jwtService from "../services/jwt.service";
import jwt from "jsonwebtoken";
import { Document, Query } from "mongoose";
class AuthController {
  public async signup(req: Request, res: Response): Promise<void> {
    const { name, email, password }: UserSignupInterface = req.body;
    console.log(req.body);
    try {
      const user: UserInterface = await User.create({ name, email, password });
      res
        .status(HttpException.SUCCESS)
        .json({ data: user, message: "Signup Successful" });
    } catch (error) {
      console.log("ERROR while signup", error);
      res
        .status(HttpException.NOT_FOUND)
        .json({ message: "error while signup", error });
    }
  }
  public async login(req: Request, res: Response): Promise<any> {
    const { email, password } = req.body;
    try {
      const user: UserInterface | null = await User.findOne({
        email,
        password,
      });
      if (!user) {
        return res
          .status(HttpException.NOT_FOUND)
          .json({ message: "Invalid Credentials" });
      }
      const accessToken: string = await jwtService.generateToken(user?._id);
      const RefreshToken: string = await jwtService.generateRefreshToken(
        user?._id
      );
      res
        .status(HttpException.SUCCESS)
        .json({ user, accessToken, RefreshToken, message: "Login Successful" });
    } catch (error) {
      console.log("ERROR while login", error);
      res
        .status(HttpException.NOT_FOUND)
        .json({ message: "error while login", error });
    }
  }
  public async refreshToken(req: Request, res: Response): Promise<any> {
    const { refreshToken, userId } = req.body;

    if (!refreshToken) {
      return res.status(401).json({ message: "Refresh Token required" });
    }

    try {
      // Verify the refresh token
      const decoded = jwt.verify(
        refreshToken,
        process.env.JWT_REFRESH_SECRET as string
      );
      console.log({ decoded });

      const user = User.findOne({
        _id: userId,
      }) as any;

      const accessToken = await jwtService.generateToken(user?._id as string);
      console.log({ accessToken });

      res.status(200).json({ accessToken, refreshToken });
    } catch (error) {
      console.log("in Catch", error);

      return res
        .status(403)
        .json({ message: "Invalid or expired refresh token" });
    }
  }
}
export default new AuthController();
