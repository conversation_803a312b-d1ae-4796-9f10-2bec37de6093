import { Request, Response } from "express";
import _package from "../model/package";
import { PackageInterface } from "../Interface/package.interface";
import { HttpException } from "../constants/httpException";

class PackagesController {
  public async createdPackage(req: Request, res: Response): Promise<void> {
    const packagePayload: PackageInterface = req.body;
    try {
      console.log({ packagePayload });
      const packageCreated: PackageInterface = await _package.create({
        ...packagePayload,
      });

      res.status(HttpException.CREATED).json({
        data: packageCreated,
        message: "package created successfully",
      });
    } catch (error) {
      console.log("ERROR while creating Package", error);
      res
        .status(HttpException.FORBIDDEN)
        .json({ message: "Error while creating package", error });
    }
  }
  public async listPackage(_req: Request, res: Response): Promise<void> {
    try {
      const packages: PackageInterface[] = await _package.find();
      res.status(HttpException.CREATED).json({
        data: packages,
        message: "All packages retrieved successfully",
      });
    } catch (error) {
      console.log("ERROR while retrieving Packages", error);
      res
        .status(HttpException.FORBIDDEN)
        .json({ message: "Error while retrieving packages" });
    }
  }

  public async updatePackage(req: Request, res: Response): Promise<void> {
    const updatePackagePayload = req.body;
    try {
      const updatedPackages = (await _package.findByIdAndUpdate(
        { _id: updatePackagePayload._id },
        updatePackagePayload
      )) as PackageInterface;
      res.status(HttpException.CREATED).json({
        data: updatedPackages,
        message: " package updated successfully",
      });
    } catch (error) {
      console.log("ERROR while updating Packages", error);
      res
        .status(HttpException.FORBIDDEN)
        .json({ message: "Error while updating packages" });
    }
  }
}

export default new PackagesController();
