import { ObjectId } from "mongoose";
import { LocationInterface } from "./location.interface";
import { PackageInterface } from "./package.interface";
import { OrderStatusENUMS } from "../constants/orderStatus";
import { QuoteInterface } from "./quote.interface";

export interface OrderInterface {
  PickupLocation: LocationInterface;
  DeliveryLocation: LocationInterface;
  Packages: PackageInterface;
  Quote: QuoteInterface;
  OrderStatus: OrderStatusENUMS;
  IsPaid: boolean;
  AssignedDriver: Array<ObjectId>;
  createdAt: Date;
  _id: string;
}
