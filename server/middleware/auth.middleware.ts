import { UserInterface, UserSignupInterface } from '../Interface/user.interface';
import user from '../model/user';
import {Request, Response, NextFunction} from 'express'

class AuthMiddleware {
  async signupValidator(req: Request, res: Response, next: NextFunction): Promise<any> {
    const { name, email, password }: UserSignupInterface = req.body;
    try {
      const existingUser: UserInterface | null = await user.findOne({ name, email, password });

      if (existingUser) {
        return res.status(400).json({ message: "User is Already exists" });
      }
      next();
    } catch (error) {
      console.log('ERROR while validating signup user', error);
    }
  }
}

export default new AuthMiddleware();
