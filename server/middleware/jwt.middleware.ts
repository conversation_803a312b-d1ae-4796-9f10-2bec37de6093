import {
  UserInterface,
  UserSignupInterface,
} from "../Interface/user.interface";
import user from "../model/user";
import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";

class JWTMiddleware {
  async signupValidator(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<any> {
    const { name, email, password }: UserSignupInterface = req.body;
    try {
      const existingUser: UserInterface | null = await user.findOne({
        name,
        email,
        password,
      });

      if (existingUser) {
        return res.status(400).json({ message: "User is Already exists" });
      }
      next();
    } catch (error) {
      console.log("ERROR while validating signup user", error);
    }
  }

  async authenticateToken(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<any> {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) return res.status(404).json({ message: "Not Token provided" });

    jwt.verify(token, process.env.JWT_SECRET as string, (err, user) => {
      if (err) {
        console.log({ err });

        return res.status(401).json({ message: err });
      }

      next();
    });
  }
}

export default new JWTMiddleware();
