import express, { Application, Request, Response } from "express";
import cors from "cors";
import * as dotenv from "dotenv";
import mongoose from "mongoose";
import authRouter from "./router/auth.router";
import locationRouter from "./router/location.router";
import packageRouter from "./router/package.router";

const app: Application = express();

dotenv.config();

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ limit: "30mb", extended: true }));

//Routes
app.use("/auth", authRouter);
app.use("/location", locationRouter);
app.use("/package", packageRouter);

const PORT: number = 8000;
const DB_URL: string = process.env.DB_URL || "mongodb://localhost:27017";

app.get("/", (req: Request, res: Response) => {
  res.send("NGS Server");
});
app.listen(PORT, () => {
  console.log("NGS is running on Port 8000");
});

mongoose.connect(DB_URL).then(() => console.log("connected with DB"));
