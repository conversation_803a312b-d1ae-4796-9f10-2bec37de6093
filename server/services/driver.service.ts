import { DriverInterface } from "../Interface/drive.interface";
import { LocationInterface } from "../Interface/location.interface";
import { ZoneInterface } from "../Interface/zone.interface";
import zone from "../model/zone";

class DriverService {

    public async assignDriver(pickupLocation: LocationInterface, deliveryLocation: LocationInterface): Promise<DriverInterface> {

        const pickupZone: string = pickupLocation.postalCode;
        const deliveryZone: string = deliveryLocation.postalCode;

       const availablePickupZone: ZoneInterface | null = await zone.findOne({postalCode: pickupZone}).populate('Driver');
       const availableDeliveryZone: ZoneInterface | null = await zone.findOne({postalCode: deliveryZone}).populate('Driver');

       if (!availablePickupZone || !availableDeliveryZone) {
           throw new Error('Out Of Service Area');
       }
       if (!availablePickupZone.isActive || !availableDeliveryZone.isActive) {
          throw new Error('Zone is not Active Right Now Please Contact Admin');
        }

        // getting all drivers
        const locationWithDrivers: Array<DriverInterface> = availableDeliveryZone.Drivers.length > 0 ? availableDeliveryZone.Drivers : availablePickupZone.Drivers;
        
        if (locationWithDrivers.length <= 0) {
            throw new Error('No Drivers Available in Zones');
        }

        //all available drivers
        const AvailableDrivers: Array<DriverInterface> = locationWithDrivers.filter(driver => driver.isAvailable);

        //return random available driver
        const driver = AvailableDrivers[Math.floor(Math.random() * locationWithDrivers.length)]
        
        return driver;
    }
}

export default new DriverService();