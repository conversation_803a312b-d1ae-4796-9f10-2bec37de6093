import jwt, { JwtPayload } from "jsonwebtoken";

class JwtService {
  public async generateToken(userId: string): Promise<string> {
    const JwtSecret: string = process.env.JWT_SECRET as string;
    const token: string = jwt.sign({ userId }, JwtSecret, {
      expiresIn: "30s",
    });
    return token;
  }

  public async generateRefreshToken(userId: string): Promise<string> {
    const JwtRefreshSecret: string = process.env.JWT_REFRESH_SECRET as string;
    const token: string = jwt.sign({ userId }, JwtRefreshSecret, {
      expiresIn: "180s",
    });
    return token;
  }

  public validateToken(token: string): string | JwtPayload {
    const JwtSecret: string = process.env.JWT_SECRET as string;
    return jwt.verify(token, JwtSecret);
  }
}

export default new JwtService();
