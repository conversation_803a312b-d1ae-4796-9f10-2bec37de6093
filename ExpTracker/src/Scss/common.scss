$navbarHeight: 65px;
$parentColor: #375682;

//mixins
@mixin flex($content, $item) {
  display: flex;
  justify-content: $content;
  align-items: $item;
}

@mixin logoTextStyle {
  color: #ffff;
  font-size: 2.2rem;
  font-weight: 500;
  letter-spacing: 0.3px;
}

@mixin ngsInput {
  .ant-form-item {
    width: 100% !important;
    margin-bottom: 10px;
  }
  input {
    height: 40px;
    border-radius: 4px;
    padding: 1.6rem 1rem;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
    color: $parentColor;
    border: 1px solid $parentColor;
  }
}

@mixin ngsPasswordInput {
  .ant-input-affix-wrapper {
    border: 1px solid $parentColor;
    border-radius: 4px;
    input {
      border: none !important;
      padding: 1.3rem 0.4rem;
    }
    .ant-input-suffix {
      font-size: 1.2rem;
    }

    .anticon.ant-input-password-icon {
      color: $parentColor !important;
    }
  }
}

@mixin ngsBtn {
  background-color: $parentColor;
  width: 100%;
  height: 48px;
  padding: 12px 2rem;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  cursor: pointer;
}

@mixin ngsLabel {
  color: $parentColor;
  font-size: 2.3rem;
  margin: 0.7rem 0;
  &::after {
    content: "";
    display: block;
    height: 4px;
    width: 40px;
    background-color: $parentColor;
    margin-top: 7px;
  }
}
