import { Form, Input } from 'antd'
import './orderEntry.scss'
import { logisticsBg2 } from '../../assets/images'

const OrderEntry = () => {
  return (
    <div className='order-entry-container'>
      <div className="img-wrapper">
        <img src={logisticsBg2} alt="" width={100}/>
      </div>
      <div className="location-form-wrapper">
       <h2>Pickup From</h2>
       <Form name='pickup'>
        <div className="form-item-wrapper">
          <label htmlFor="addr1">Address Line 1</label>
         <Form.Item name={'addressline1'}>
          <Input placeholder='Address Line 1' id='addr1'/>
         </Form.Item>
        </div>
        <div className="form-item-wrapper">
        <label htmlFor="pin">Pincode</label>
         <Form.Item name={'pincode'}>
          <Input placeholder='Pincode' id='pin'/>
         </Form.Item>
        </div>
        <div className="form-item-wrapper">
         <Form.Item name={'addressline1'}>
          <Input placeholder='Address Line 1' />
         </Form.Item>
        </div>
        <div className="form-item-wrapper">
         <Form.Item name={'addressline1'}>
          <Input placeholder='Address Line 1' />
         </Form.Item>
        </div>
        
       </Form>
      </div>
    </div>
  )
}

export default OrderEntry