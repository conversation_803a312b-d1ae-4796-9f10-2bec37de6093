import React, { useState } from "react";
import "./auth.scss";
import { logisticsBg } from "../../assets/images";
import { Button, Form, FormInstance, Input } from "antd";
import { config } from "../../Config";
import { LockOutlined, MailOutlined, UserOutlined } from "@ant-design/icons";
import { URLS } from "../../constants/url";
import { API } from "../../Api/api";
import { NavigateFunction, useNavigate } from "react-router-dom";

interface UserInterface {
  name?: string;
  email: string;
  password: string;
}

const Auth: React.FC = () => {
  const [isSignup, setIsSignup] = useState<boolean>(false);
  const [form]: FormInstance[] = Form.useForm();
  const navigate: NavigateFunction = useNavigate();

  const onFinish = async (values: UserInterface): Promise<void> => {
    try {
      if (isSignup) {
        const res = await API.post(URLS.SIGNUP_API, values);
        if (res.status === 200) {
          form.resetFields();
        }
        return;
      }
      console.log(values);
      
      const res = await API.post(URLS.LOGIN_API, values);
      if (res.status === 200) {
        form.resetFields();
        navigate('/order/entry');
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <main className="auth-container">
      <div className="img-wrapper">
        <img src={logisticsBg} />
      </div>
      <div className="auth-form-wrapper">
        <div className="auth-form">
          <h1>{isSignup ? 'Sign up' : 'Log in'}</h1>
          <p>
            Welcome in {config.PORTAL_NAME} Portal, Please enter your details.
          </p>

          <Form name="auth" onFinish={onFinish} form={form}>
            {isSignup && (
              <div className="form-item-wrapper">
                <label htmlFor="email">Name</label>
                <Form.Item
                  name={"name"}
                  rules={[
                    {
                      required: true,
                      message: "Name is required",
                    },
                  ]}
                  className="auth-input"
                >
                  <Input
                    placeholder="Enter your Name"
                    id="name"
                    prefix={
                      <UserOutlined
                        style={{
                          fontSize: "1.1rem",
                          color: config.PARENT_COLOR,
                        }}
                      />
                    }
                  />
                </Form.Item>
              </div>
            )}
            <div className="form-item-wrapper">
              <label htmlFor="email">Email</label>
              <Form.Item
                name={"email"}
                rules={[
                  {
                    required: true,
                    message: "Email is required",
                  },
                ]}
                className="auth-input"
              >
                <Input
                  placeholder="Enter your Email"
                  id="email"
                  prefix={
                    <MailOutlined
                      style={{ fontSize: "1.1rem", color: config.PARENT_COLOR }}
                    />
                  }
                />
              </Form.Item>
            </div>
            <div className="form-item-wrapper">
              <label htmlFor="password">Password</label>
              <Form.Item
                name={"password"}
                rules={[
                  {
                    required: true,
                    message: "Password is required",
                  },
                ]}
                className="auth-input"
              >
                <Input.Password
                  prefix={
                    <LockOutlined
                      style={{ fontSize: "1.1rem", color: config.PARENT_COLOR }}
                    />
                  }
                  placeholder="Enter your Password"
                  id="password"
                />
              </Form.Item>
            </div>
            <h4>
              <>
                {isSignup ? (
                  <>Already have an Account? &nbsp; </>
                ) : (
                  <>Don't have an Account? &nbsp; </>
                )}
                <span
                  className="signup-mode-btn"
                  onClick={() => setIsSignup(!isSignup)}
                >
                  {isSignup ? "Login" : "Sign up"}
                </span>
              </>
            </h4>
            <Button htmlType="submit" className="login-btn">
              {isSignup ? "Sign up" : "Login"}
            </Button>
          </Form>
        </div>
      </div>
    </main>
  );
};

export default Auth;
