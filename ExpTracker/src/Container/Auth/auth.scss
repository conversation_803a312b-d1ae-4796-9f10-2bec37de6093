@import "../../Scss/common.scss";

.auth-container {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #f2f2f2;
  display: flex;

  .img-wrapper {
    height: 100%;
    width: 55%;
    img {
      height: 100%;
      width: 100%;
      opacity: 0.5;
    }
  }

  .auth-form-wrapper {
    @include flex(center, center);
    flex: 1;
    .auth-form {
      padding: 2rem 3rem;
      width: 75%;
      h1 {
        @include ngsLabel;
      }
      p {
        margin: 0.7rem 0;
        color: grey;
        margin: 1rem 0;
        font-size: 14px;
      }

      .form-item-wrapper {
        @include ngsInput;
        @include ngsPasswordInput;
        @include flex(start, start);
        flex-direction: column;
        margin: 1rem 0;
        width: 100%;

        label {
          color: $parentColor;
          font-size: 16px;
          font-weight: 500;
          letter-spacing: 0.3px;
          margin-bottom: 8px;
        }
      }
      .login-btn {
        @include ngsBtn;
      }
      h4 {
        color: $parentColor;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.3px;
        margin-bottom: 1rem;

        .signup-mode-btn {
          @include ngsBtn;
          margin-top: 1.2rem;
          padding: 3px 10px;
        }
      }
    }
  }
}
