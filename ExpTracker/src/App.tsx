import { BrowserRouter } from "react-router-dom";
import "./App.css";
import AppRouter from "./Router/AppRouter";
import { ConfigProvider } from "antd";

function App() {
  return (
    <>
      <BrowserRouter>
        <ConfigProvider
          theme={{
            token: {
              // Seed Token
              colorPrimary: "red",
              borderRadius: 2,

              // Alias <PERSON>ken
              colorBgContainer: "red",
            },
          }}
        >
          <AppRouter />
        </ConfigProvider>
      </BrowserRouter>
    </>
  );
}

export default App;
