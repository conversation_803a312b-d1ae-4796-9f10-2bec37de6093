import { NavLink } from "react-router-dom";
import { config } from "../../Config";
import "./navbar.scss";
import { Button } from "antd";

const Navbar: React.FC = () => {
  return (
    <nav className="nav">
      <h1 className="logo-txt">{config.APP_NAME}</h1>
      <div className="nav-links">
        <NavLink to={'/order/entry'} className="link" activeClassName={'active'}>Order Entry</NavLink>
        <NavLink to={'/order/list'} className="link" activeClassName={'active'}>My Orders</NavLink>
        <NavLink to={'/location/list'} className="link" activeClassName={'active'}>Locations</NavLink>
      </div>
      <Button className="logout-btn">Logout</Button>
    </nav>
  );
};

export default Navbar;
