@import "../../Scss/common.scss";

.nav {
  @include flex(space-between, center);
  height: $navbarHeight;
  width: 100%;
  padding: 0 1rem;
  // background-color: rgba(30, 143, 255, 0.214);
  border-bottom: 1px solid rgba(0, 0, 0, 0.19);
  box-shadow: rgba(0, 0, 0, 0.069) 0px 1px 4px;

  .logo-txt {
    @include logoTextStyle;
    color: $parentColor;
  }

  .nav-links {
    @include flex(space-between, center);
    gap: 2rem;
    .link {
      text-decoration: none;
      color: $parentColor
    }

    .active {
     &::after{
        content: '';
        display: block;
        background-color: $parentColor;
        height: 4px;
        width: 100%;
        margin-top: 5px;
        transition: .4s all ease;
        border-radius: 2px;
     }
    }
  }

  .logout-btn {
    @include ngsBtn;
    width: fit-content;
  }
}
